# 🛠️ 智能PDF阅读器开发环境配置指南

## 📋 环境要求

### 系统要求
- **操作系统**: Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **内存**: 最少8GB RAM (推荐16GB)
- **存储**: 至少20GB可用空间
- **网络**: 稳定的互联网连接（用于下载依赖）

---

## 🔧 第一步：安装Flutter SDK

### Windows安装
```powershell
# 1. 下载Flutter SDK
# 访问 https://docs.flutter.dev/get-started/install/windows
# 下载最新稳定版本的Flutter SDK

# 2. 解压到指定目录
# 解压到 C:\flutter (避免路径包含空格)

# 3. 添加环境变量
# 将 C:\flutter\bin 添加到系统PATH环境变量

# 4. 验证安装
flutter doctor
```

### macOS安装
```bash
# 1. 使用Homebrew安装（推荐）
brew install flutter

# 或者手动下载
# 访问 https://docs.flutter.dev/get-started/install/macos
# 下载并解压Flutter SDK

# 2. 添加到PATH（如果手动安装）
echo 'export PATH="$PATH:/path/to/flutter/bin"' >> ~/.zshrc
source ~/.zshrc

# 3. 验证安装
flutter doctor
```

### Ubuntu安装
```bash
# 1. 安装依赖
sudo apt update
sudo apt install curl git unzip xz-utils zip libglu1-mesa

# 2. 下载Flutter SDK
cd ~/development
wget https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.16.0-stable.tar.xz
tar xf flutter_linux_3.16.0-stable.tar.xz

# 3. 添加到PATH
echo 'export PATH="$PATH:$HOME/development/flutter/bin"' >> ~/.bashrc
source ~/.bashrc

# 4. 验证安装
flutter doctor
```

---

## 🦀 第二步：安装Rust工具链

### 所有平台通用安装
```bash
# 1. 安装Rustup（Rust工具链管理器）
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 2. 重新加载环境变量
source ~/.cargo/env  # Linux/macOS
# 或重启终端

# 3. 验证安装
rustc --version
cargo --version

# 4. 安装必要的目标平台
rustup target add aarch64-apple-ios      # iOS (仅macOS)
rustup target add aarch64-linux-android  # Android
rustup target add armv7-linux-androideabi # Android
rustup target add i686-linux-android     # Android
rustup target add x86_64-linux-android   # Android
```

### Windows额外配置
```powershell
# 安装Visual Studio Build Tools
# 下载并安装 Visual Studio Build Tools 2022
# 确保包含 "C++ build tools" 工作负载

# 验证MSVC工具链
rustup toolchain install stable-msvc
rustup default stable-msvc
```

---

## 📱 第三步：配置移动端开发环境

### Android开发环境
```bash
# 1. 安装Android Studio
# 下载地址: https://developer.android.com/studio

# 2. 安装Android SDK
# 在Android Studio中安装以下组件:
# - Android SDK Platform-Tools
# - Android SDK Build-Tools
# - Android SDK Platform (API 33+)
# - Android NDK

# 3. 配置环境变量
export ANDROID_HOME=$HOME/Android/Sdk  # Linux/macOS
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/platform-tools

# Windows (添加到系统环境变量):
# ANDROID_HOME = C:\Users\<USER>\AppData\Local\Android\Sdk
# PATH += %ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools

# 4. 验证配置
flutter doctor
```

### iOS开发环境（仅macOS）
```bash
# 1. 安装Xcode
# 从App Store安装最新版本的Xcode

# 2. 安装Xcode命令行工具
sudo xcode-select --install

# 3. 接受Xcode许可协议
sudo xcodebuild -license accept

# 4. 安装CocoaPods
sudo gem install cocoapods

# 5. 验证配置
flutter doctor
```

---

## 🔗 第四步：安装flutter_rust_bridge

### 安装cargo-flutter
```bash
# 1. 安装flutter_rust_bridge代码生成器
cargo install flutter_rust_bridge_codegen

# 2. 安装LLVM（用于代码生成）
# Windows (使用Chocolatey):
choco install llvm

# macOS:
brew install llvm

# Ubuntu:
sudo apt install llvm-dev libclang-dev clang

# 3. 验证安装
flutter_rust_bridge_codegen --version
```

---

## 🛠️ 第五步：安装开发工具

### 必需的开发工具
```bash
# 1. 安装VS Code（推荐IDE）
# 下载地址: https://code.visualstudio.com/

# 2. 安装VS Code扩展
# - Flutter
# - Dart
# - Rust-analyzer
# - Better TOML
# - Error Lens

# 3. 安装Git
# Windows: https://git-scm.com/download/win
# macOS: brew install git
# Ubuntu: sudo apt install git
```

### 可选的开发工具
```bash
# 1. 安装Android模拟器
# 在Android Studio中创建AVD (Android Virtual Device)

# 2. 安装iOS模拟器（仅macOS）
# 在Xcode中安装iOS Simulator

# 3. 安装调试工具
cargo install cargo-watch  # 文件变化自动重编译
cargo install cargo-edit   # 简化依赖管理
```

---

## ✅ 第六步：验证环境配置

### 运行完整检查
```bash
# 1. 检查Flutter环境
flutter doctor -v

# 2. 检查Rust环境
rustc --version
cargo --version
rustup show

# 3. 检查flutter_rust_bridge
flutter_rust_bridge_codegen --version

# 4. 测试创建Flutter项目
flutter create test_project
cd test_project
flutter run  # 确保能在模拟器/设备上运行
```

### 预期输出示例
```
Doctor summary (to see all details, run flutter doctor -v):
[✓] Flutter (Channel stable, 3.16.0, on macOS 14.0)
[✓] Android toolchain - develop for Android devices (Android SDK version 34.0.0)
[✓] Xcode - develop for iOS and macOS (Xcode 15.0)
[✓] Chrome - develop for the web
[✓] Android Studio (version 2023.1)
[✓] VS Code (version 1.84.0)
[✓] Connected device (2 available)
[✓] Network resources

• No issues found!
```

---

## 🚨 常见问题解决

### Flutter Doctor问题
```bash
# 问题1: Android licenses not accepted
flutter doctor --android-licenses

# 问题2: Xcode not properly configured
sudo xcode-select --switch /Applications/Xcode.app/Contents/Developer

# 问题3: VS Code not detected
# 确保VS Code在PATH中，或重新安装
```

### Rust编译问题
```bash
# 问题1: 链接器错误 (Windows)
# 安装Visual Studio Build Tools with C++ workload

# 问题2: 权限问题 (Linux/macOS)
sudo chown -R $(whoami) ~/.cargo

# 问题3: 网络问题
# 配置Cargo镜像源 (中国用户)
mkdir -p ~/.cargo
echo '[source.crates-io]
registry = "https://github.com/rust-lang/crates.io-index"
replace-with = "ustc"
[source.ustc]
registry = "git://mirrors.ustc.edu.cn/crates.io-index"' > ~/.cargo/config
```

---

## 📝 环境配置检查清单

- [ ] Flutter SDK安装并添加到PATH
- [ ] Rust工具链安装完成
- [ ] Android Studio和SDK配置完成
- [ ] Xcode和iOS工具安装完成（macOS）
- [ ] flutter_rust_bridge_codegen安装成功
- [ ] VS Code和必要扩展安装完成
- [ ] Git配置完成
- [ ] flutter doctor检查通过
- [ ] 能够创建和运行Flutter项目
- [ ] Rust编译环境正常工作

---

## 🎯 下一步

环境配置完成后，您可以继续进行：
1. **项目结构创建** - 创建Flutter项目和Rust库
2. **FFI通信配置** - 配置flutter_rust_bridge
3. **基础UI框架搭建** - 实现主界面和导航系统

---

*配置指南版本: v1.0*  
*适用于: Flutter 3.16+ & Rust 1.70+*
