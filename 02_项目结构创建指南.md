# 🏗️ 智能PDF阅读器项目结构创建指南

## 📁 项目整体结构

```
smart_pdf_reader/
├── flutter_app/                 # Flutter前端应用
│   ├── lib/
│   │   ├── main.dart
│   │   ├── bridge/              # FFI桥接层
│   │   ├── screens/             # 页面
│   │   ├── widgets/             # 组件
│   │   ├── models/              # 数据模型
│   │   ├── services/            # 服务层
│   │   └── utils/               # 工具类
│   ├── android/
│   ├── ios/
│   ├── pubspec.yaml
│   └── analysis_options.yaml
├── rust_core/                   # Rust后端核心
│   ├── src/
│   │   ├── lib.rs
│   │   ├── api/                 # FFI API接口
│   │   ├── core/                # 核心业务逻辑
│   │   ├── database/            # 数据库管理
│   │   ├── ocr/                 # OCR引擎管理
│   │   ├── tts/                 # TTS引擎管理
│   │   └── utils/               # 工具模块
│   ├── Cargo.toml
│   └── build.rs
├── docs/                        # 项目文档
├── scripts/                     # 构建脚本
└── README.md
```

---

## 🚀 第一步：创建Flutter项目

### 创建Flutter应用
```bash
# 1. 创建项目根目录
mkdir smart_pdf_reader
cd smart_pdf_reader

# 2. 创建Flutter应用
flutter create flutter_app
cd flutter_app

# 3. 清理默认代码
rm lib/main.dart
rm test/widget_test.dart
```

### 配置pubspec.yaml
```yaml
name: smart_pdf_reader
description: 智能PDF阅读器 - 支持OCR重排和语音阅读
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  
  # 状态管理
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  
  # FFI桥接
  flutter_rust_bridge: ^2.0.0
  ffi: ^2.1.0
  
  # UI组件
  cupertino_icons: ^1.0.6
  material_color_utilities: ^0.5.0
  
  # 文件处理
  path_provider: ^2.1.1
  file_picker: ^6.1.1
  permission_handler: ^11.1.0
  
  # PDF处理
  syncfusion_flutter_pdfviewer: ^23.2.7
  
  # 数据库
  sqflite: ^2.3.0
  
  # 网络和存储
  dio: ^5.3.2
  shared_preferences: ^2.2.2
  
  # 工具类
  uuid: ^4.2.1
  intl: ^0.18.1
  logger: ^2.0.2+1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  
  # 代码生成
  riverpod_generator: ^2.3.9
  build_runner: ^2.4.7
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/fonts/
    
  fonts:
    - family: SourceHanSans
      fonts:
        - asset: assets/fonts/SourceHanSans-Regular.ttf
        - asset: assets/fonts/SourceHanSans-Bold.ttf
          weight: 700
```

---

## 🦀 第二步：创建Rust核心库

### 创建Rust项目
```bash
# 回到项目根目录
cd ..

# 创建Rust库项目
cargo new --lib rust_core
cd rust_core
```

### 配置Cargo.toml
```toml
[package]
name = "rust_core"
version = "0.1.0"
edition = "2021"

[lib]
name = "rust_core"
crate-type = ["cdylib", "staticlib"]

[dependencies]
# FFI桥接
flutter_rust_bridge = "2.0"
tokio = { version = "1.35", features = ["full"] }
anyhow = "1.0"
thiserror = "1.0"

# 数据库
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# OCR相关
tesseract = "0.13"
image = "0.24"

# 文件处理
pdf = "0.8"
uuid = { version = "1.6", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }

# 日志
tracing = "0.1"
tracing-subscriber = "0.3"

# 工具
lazy_static = "1.4"
parking_lot = "0.12"

[build-dependencies]
flutter_rust_bridge_codegen = "2.0"

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
```

### 创建build.rs构建脚本
```rust
use flutter_rust_bridge_codegen::{
    config_parse, frb_codegen, get_symbols_if_no_duplicates, RawOpts,
};

const RUST_INPUT: &str = "src/api/mod.rs";
const DART_OUTPUT: &str = "../flutter_app/lib/bridge/native.dart";

fn main() {
    // 告诉Cargo在输入文件变化时重新构建
    println!("cargo:rerun-if-changed={}", RUST_INPUT);
    
    // 生成FFI绑定代码
    let raw_opts = RawOpts {
        rust_input: vec![RUST_INPUT.to_string()],
        dart_output: vec![DART_OUTPUT.to_string()],
        c_output: Some(vec!["../flutter_app/ios/Classes/frb.h".to_string()]),
        inline: true,
        wasm: false,
        ..Default::default()
    };
    
    let configs = config_parse(raw_opts);
    let all_symbols = get_symbols_if_no_duplicates(&configs).unwrap();
    
    for config in configs.iter() {
        frb_codegen(config, &all_symbols).unwrap();
    }
}
```

---

## 🔗 第三步：配置FFI桥接

### 创建Rust API接口
```bash
# 在rust_core/src目录下创建API模块
mkdir -p src/api
mkdir -p src/core
mkdir -p src/database
mkdir -p src/ocr
mkdir -p src/tts
mkdir -p src/utils
```

### src/lib.rs
```rust
//! 智能PDF阅读器Rust核心库
//! 
//! 本库提供PDF处理、OCR识别、TTS语音合成等核心功能
//! 通过FFI接口与Flutter前端通信

pub mod api;
pub mod core;
pub mod database;
pub mod ocr;
pub mod tts;
pub mod utils;

use flutter_rust_bridge::frb;

/// 初始化Rust核心库
/// 
/// 设置日志系统和全局配置
#[frb(sync)]
pub fn init_rust_core() -> anyhow::Result<String> {
    // 初始化日志系统
    tracing_subscriber::fmt::init();
    
    tracing::info!("智能PDF阅读器Rust核心库初始化成功");
    Ok("Rust核心库初始化完成".to_string())
}

/// 获取库版本信息
#[frb(sync)]
pub fn get_version() -> String {
    env!("CARGO_PKG_VERSION").to_string()
}
```

### src/api/mod.rs
```rust
//! FFI API接口模块
//! 
//! 定义所有与Flutter通信的接口

use flutter_rust_bridge::frb;
use crate::core::*;

/// PDF文档信息
#[derive(Debug, Clone)]
pub struct DocumentInfo {
    pub id: String,
    pub title: String,
    pub author: String,
    pub page_count: i32,
    pub file_path: String,
    pub created_at: String,
}

/// OCR识别结果
#[derive(Debug, Clone)]
pub struct OcrResult {
    pub page_number: i32,
    pub text_blocks: Vec<TextBlock>,
    pub confidence: f32,
    pub processing_time: f32,
}

/// 文本块信息
#[derive(Debug, Clone)]
pub struct TextBlock {
    pub id: String,
    pub text: String,
    pub x: f32,
    pub y: f32,
    pub width: f32,
    pub height: f32,
    pub confidence: f32,
}

/// 打开PDF文档
#[frb(sync)]
pub fn open_document(file_path: String) -> anyhow::Result<DocumentInfo> {
    tracing::info!("打开PDF文档: {}", file_path);
    
    // TODO: 实现PDF文档解析
    Ok(DocumentInfo {
        id: uuid::Uuid::new_v4().to_string(),
        title: "示例文档".to_string(),
        author: "未知作者".to_string(),
        page_count: 100,
        file_path,
        created_at: chrono::Utc::now().to_rfc3339(),
    })
}

/// 开始OCR识别
pub async fn start_ocr_recognition(
    document_id: String,
    start_page: i32,
) -> anyhow::Result<String> {
    tracing::info!("开始OCR识别: 文档ID={}, 起始页={}", document_id, start_page);
    
    // TODO: 实现OCR识别逻辑
    Ok("OCR识别已启动".to_string())
}

/// 获取OCR识别进度
#[frb(sync)]
pub fn get_ocr_progress(document_id: String) -> anyhow::Result<f32> {
    // TODO: 实现进度查询
    Ok(0.0)
}
```

---

## 📱 第四步：创建Flutter桥接层

### 创建Flutter桥接目录结构
```bash
cd ../flutter_app
mkdir -p lib/bridge
mkdir -p lib/screens
mkdir -p lib/widgets
mkdir -p lib/models
mkdir -p lib/services
mkdir -p lib/utils
```

### lib/main.dart
```dart
/// 智能PDF阅读器主应用入口
/// 
/// 功能包括：
/// - 应用初始化和配置
/// - 主题设置
/// - 路由配置
/// - 全局状态管理

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:smart_pdf_reader/bridge/rust_bridge.dart';
import 'package:smart_pdf_reader/screens/home_screen.dart';
import 'package:smart_pdf_reader/utils/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化Rust核心库
  await RustBridge.init();
  
  runApp(
    const ProviderScope(
      child: SmartPdfReaderApp(),
    ),
  );
}

/// 主应用组件
class SmartPdfReaderApp extends StatelessWidget {
  const SmartPdfReaderApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '智能PDF阅读器',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      home: const HomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
```

### lib/bridge/rust_bridge.dart
```dart
/// Rust FFI桥接封装
/// 
/// 提供与Rust核心库通信的高级接口

import 'dart:ffi';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:smart_pdf_reader/bridge/native.dart';

/// Rust桥接管理器
class RustBridge {
  static late RustImpl _api;
  static bool _initialized = false;

  /// 初始化Rust桥接
  static Future<void> init() async {
    if (_initialized) return;

    try {
      // 加载动态库
      final dylib = _loadLibrary();
      _api = RustImpl(dylib);
      
      // 初始化Rust核心库
      final result = _api.initRustCore();
      debugPrint('Rust初始化结果: $result');
      
      _initialized = true;
      debugPrint('Rust桥接初始化成功');
    } catch (e) {
      debugPrint('Rust桥接初始化失败: $e');
      rethrow;
    }
  }

  /// 加载动态库
  static DynamicLibrary _loadLibrary() {
    if (Platform.isAndroid) {
      return DynamicLibrary.open('librust_core.so');
    } else if (Platform.isIOS) {
      return DynamicLibrary.process();
    } else if (Platform.isWindows) {
      return DynamicLibrary.open('rust_core.dll');
    } else if (Platform.isMacOS) {
      return DynamicLibrary.open('librust_core.dylib');
    } else if (Platform.isLinux) {
      return DynamicLibrary.open('librust_core.so');
    } else {
      throw UnsupportedError('不支持的平台: ${Platform.operatingSystem}');
    }
  }

  /// 获取API实例
  static RustImpl get api {
    if (!_initialized) {
      throw StateError('Rust桥接尚未初始化，请先调用 RustBridge.init()');
    }
    return _api;
  }

  /// 获取版本信息
  static String getVersion() {
    return api.getVersion();
  }
}
```

---

## 🎨 第五步：创建基础UI组件

### lib/utils/app_theme.dart
```dart
/// 应用主题配置
/// 
/// 定义亮色和暗色主题样式

import 'package:flutter/material.dart';

class AppTheme {
  // 主色调
  static const Color primaryColor = Color(0xFF2196F3);
  static const Color secondaryColor = Color(0xFF03DAC6);
  static const Color errorColor = Color(0xFFB00020);

  /// 亮色主题
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.light,
      ),
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: 0,
        scrolledUnderElevation: 1,
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  /// 暗色主题
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.dark,
      ),
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: 0,
        scrolledUnderElevation: 1,
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 12,
          ),
        ),
      ),
    );
  }
}
```

### lib/screens/home_screen.dart
```dart
/// 主页面
/// 
/// 显示书籍列表和主要功能入口

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('智能PDF阅读器'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // TODO: 打开设置页面
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.picture_as_pdf,
              size: 64,
              color: Colors.blue,
            ),
            SizedBox(height: 16),
            Text(
              '智能PDF阅读器',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              '支持OCR重排和语音阅读',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: 打开文件选择器
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
```

---

## 🔧 第六步：配置构建脚本

### scripts/build.sh
```bash
#!/bin/bash
# 智能PDF阅读器构建脚本

set -e

echo "🚀 开始构建智能PDF阅读器..."

# 构建Rust核心库
echo "📦 构建Rust核心库..."
cd rust_core
cargo build --release
cd ..

# 生成FFI绑定代码
echo "🔗 生成FFI绑定代码..."
cd rust_core
cargo build
cd ..

# 构建Flutter应用
echo "📱 构建Flutter应用..."
cd flutter_app
flutter pub get
flutter pub run build_runner build --delete-conflicting-outputs
flutter build apk --release
cd ..

echo "✅ 构建完成！"
```

### scripts/clean.sh
```bash
#!/bin/bash
# 清理构建产物

echo "🧹 清理构建产物..."

# 清理Rust构建产物
cd rust_core
cargo clean
cd ..

# 清理Flutter构建产物
cd flutter_app
flutter clean
rm -rf .dart_tool/
rm -rf build/
cd ..

echo "✅ 清理完成！"
```

---

## ✅ 验证项目结构

### 运行验证脚本
```bash
# 回到项目根目录
cd smart_pdf_reader

# 验证Rust项目
cd rust_core
cargo check
cd ..

# 验证Flutter项目
cd flutter_app
flutter pub get
flutter analyze
cd ..

echo "✅ 项目结构创建完成！"
```

---

## 📝 下一步

项目结构创建完成后，您可以继续进行：
1. **基础UI框架搭建** - 完善界面组件和导航
2. **数据库架构设计** - 实现三层数据库架构
3. **OCR引擎集成** - 集成Tesseract等OCR引擎

---

*项目结构指南版本: v1.0*  
*适用于: Flutter 3.16+ & Rust 1.70+*
