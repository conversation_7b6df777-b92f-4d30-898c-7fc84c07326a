# 🔄 后台OCR识别机制实现

## 🦀 Rust后台识别系统

### rust_core/src/core/background_ocr.rs
```rust
//! 后台OCR识别系统
//! 
//! 功能包括：
//! - 后台持续OCR识别
//! - 智能续传机制
//! - 任务队列管理
//! - 进度跟踪和状态管理

use crate::database::{DatabaseManager, repositories::{BookRepository, OcrRepository}};
use crate::ocr::OcrManager;
use crate::core::OcrProcessor;
use anyhow::{Result, anyhow};
use std::sync::Arc;
use std::collections::{HashMap, VecDeque};
use tokio::sync::{RwLock, Mutex, mpsc, oneshot};
use tokio::time::{Duration, sleep, Instant};
use tracing::{info, warn, error, debug};
use serde::{Serialize, Deserialize};
use uuid::Uuid;

/// 后台OCR任务
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OcrTask {
    pub id: String,
    pub book_id: String,
    pub pdf_path: String,
    pub page_number: i32,
    pub language: String,
    pub priority: TaskPriority,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub attempts: u32,
    pub max_attempts: u32,
}

/// 任务优先级
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum TaskPriority {
    Low = 1,
    Normal = 2,
    High = 3,
    Urgent = 4,
}

/// 任务状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TaskStatus {
    Pending,
    Running,
    Completed,
    Failed(String),
    Cancelled,
}

/// OCR任务结果
#[derive(Debug, Clone)]
pub struct TaskResult {
    pub task_id: String,
    pub status: TaskStatus,
    pub processing_time: Duration,
    pub error_message: Option<String>,
}

/// 后台OCR管理器
pub struct BackgroundOcrManager {
    db_manager: Arc<DatabaseManager>,
    ocr_processor: Arc<OcrProcessor>,
    task_queue: Arc<Mutex<VecDeque<OcrTask>>>,
    running_tasks: Arc<RwLock<HashMap<String, OcrTask>>>,
    task_results: Arc<RwLock<HashMap<String, TaskResult>>>,
    worker_handles: Vec<tokio::task::JoinHandle<()>>,
    control_tx: Option<mpsc::UnboundedSender<ControlMessage>>,
    config: BackgroundOcrConfig,
    is_running: Arc<RwLock<bool>>,
}

/// 后台OCR配置
#[derive(Debug, Clone)]
pub struct BackgroundOcrConfig {
    pub max_concurrent_workers: usize,
    pub task_timeout: Duration,
    pub retry_delay: Duration,
    pub max_queue_size: usize,
    pub auto_start: bool,
    pub pause_on_battery_low: bool,
    pub pause_on_thermal_throttle: bool,
}

impl Default for BackgroundOcrConfig {
    fn default() -> Self {
        Self {
            max_concurrent_workers: 2, // 限制并发数避免过度占用资源
            task_timeout: Duration::from_secs(300), // 5分钟超时
            retry_delay: Duration::from_secs(30),
            max_queue_size: 1000,
            auto_start: true,
            pause_on_battery_low: true,
            pause_on_thermal_throttle: true,
        }
    }
}

/// 控制消息
#[derive(Debug)]
enum ControlMessage {
    Start,
    Stop,
    Pause,
    Resume,
    AddTask(OcrTask),
    CancelTask(String),
    GetStatus(oneshot::Sender<BackgroundStatus>),
}

/// 后台状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BackgroundStatus {
    pub is_running: bool,
    pub is_paused: bool,
    pub queue_size: usize,
    pub running_tasks: usize,
    pub completed_tasks: usize,
    pub failed_tasks: usize,
    pub total_processing_time: Duration,
}

impl BackgroundOcrManager {
    /// 创建新的后台OCR管理器
    pub fn new(
        db_manager: Arc<DatabaseManager>,
        ocr_processor: Arc<OcrProcessor>,
        config: BackgroundOcrConfig,
    ) -> Self {
        Self {
            db_manager,
            ocr_processor,
            task_queue: Arc::new(Mutex::new(VecDeque::new())),
            running_tasks: Arc::new(RwLock::new(HashMap::new())),
            task_results: Arc::new(RwLock::new(HashMap::new())),
            worker_handles: Vec::new(),
            control_tx: None,
            config,
            is_running: Arc::new(RwLock::new(false)),
        }
    }

    /// 启动后台OCR服务
    pub async fn start(&mut self) -> Result<()> {
        if *self.is_running.read().await {
            warn!("后台OCR服务已经在运行");
            return Ok(());
        }

        info!("启动后台OCR服务，工作线程数: {}", self.config.max_concurrent_workers);

        // 创建控制通道
        let (control_tx, mut control_rx) = mpsc::unbounded_channel();
        self.control_tx = Some(control_tx);

        // 启动控制器
        let is_running = Arc::clone(&self.is_running);
        let task_queue = Arc::clone(&self.task_queue);
        let running_tasks = Arc::clone(&self.running_tasks);
        let task_results = Arc::clone(&self.task_results);
        let ocr_processor = Arc::clone(&self.ocr_processor);
        let config = self.config.clone();

        let controller_handle = tokio::spawn(async move {
            let mut is_paused = false;
            let mut worker_handles = Vec::new();

            // 启动工作线程
            for worker_id in 0..config.max_concurrent_workers {
                let handle = Self::spawn_worker(
                    worker_id,
                    Arc::clone(&task_queue),
                    Arc::clone(&running_tasks),
                    Arc::clone(&task_results),
                    Arc::clone(&ocr_processor),
                    config.clone(),
                );
                worker_handles.push(handle);
            }

            // 处理控制消息
            while let Some(message) = control_rx.recv().await {
                match message {
                    ControlMessage::Start => {
                        *is_running.write().await = true;
                        is_paused = false;
                        info!("后台OCR服务已启动");
                    }
                    ControlMessage::Stop => {
                        *is_running.write().await = false;
                        info!("后台OCR服务已停止");
                        break;
                    }
                    ControlMessage::Pause => {
                        is_paused = true;
                        info!("后台OCR服务已暂停");
                    }
                    ControlMessage::Resume => {
                        is_paused = false;
                        info!("后台OCR服务已恢复");
                    }
                    ControlMessage::AddTask(task) => {
                        if !is_paused {
                            let mut queue = task_queue.lock().await;
                            if queue.len() < config.max_queue_size {
                                // 按优先级插入任务
                                let insert_pos = queue.iter()
                                    .position(|t| t.priority < task.priority)
                                    .unwrap_or(queue.len());
                                queue.insert(insert_pos, task.clone());
                                debug!("添加OCR任务: {}", task.id);
                            } else {
                                warn!("任务队列已满，丢弃任务: {}", task.id);
                            }
                        }
                    }
                    ControlMessage::CancelTask(task_id) => {
                        // 从队列中移除任务
                        let mut queue = task_queue.lock().await;
                        queue.retain(|t| t.id != task_id);
                        
                        // 标记运行中的任务为取消
                        let mut results = task_results.write().await;
                        results.insert(task_id.clone(), TaskResult {
                            task_id: task_id.clone(),
                            status: TaskStatus::Cancelled,
                            processing_time: Duration::ZERO,
                            error_message: None,
                        });
                        
                        info!("取消OCR任务: {}", task_id);
                    }
                    ControlMessage::GetStatus(response_tx) => {
                        let queue_size = task_queue.lock().await.len();
                        let running_tasks_count = running_tasks.read().await.len();
                        let results = task_results.read().await;
                        
                        let completed_tasks = results.values()
                            .filter(|r| matches!(r.status, TaskStatus::Completed))
                            .count();
                        
                        let failed_tasks = results.values()
                            .filter(|r| matches!(r.status, TaskStatus::Failed(_)))
                            .count();
                        
                        let total_processing_time = results.values()
                            .map(|r| r.processing_time)
                            .sum();

                        let status = BackgroundStatus {
                            is_running: *is_running.read().await,
                            is_paused,
                            queue_size,
                            running_tasks: running_tasks_count,
                            completed_tasks,
                            failed_tasks,
                            total_processing_time,
                        };

                        let _ = response_tx.send(status);
                    }
                }
            }

            // 等待所有工作线程结束
            for handle in worker_handles {
                handle.abort();
            }
        });

        self.worker_handles.push(controller_handle);

        // 启动服务
        if let Some(tx) = &self.control_tx {
            tx.send(ControlMessage::Start)?;
        }

        Ok(())
    }

    /// 停止后台OCR服务
    pub async fn stop(&mut self) -> Result<()> {
        if let Some(tx) = &self.control_tx {
            tx.send(ControlMessage::Stop)?;
        }

        // 等待所有工作线程结束
        for handle in self.worker_handles.drain(..) {
            handle.await?;
        }

        *self.is_running.write().await = false;
        info!("后台OCR服务已完全停止");
        Ok(())
    }

    /// 添加OCR任务
    pub async fn add_task(&self, task: OcrTask) -> Result<()> {
        if let Some(tx) = &self.control_tx {
            tx.send(ControlMessage::AddTask(task))?;
        }
        Ok(())
    }

    /// 为书籍创建OCR任务队列
    pub async fn create_book_ocr_tasks(
        &self,
        book_id: &str,
        pdf_path: &str,
        language: &str,
        start_page: Option<i32>,
    ) -> Result<Vec<String>> {
        info!("为书籍创建OCR任务队列: {}", book_id);

        // 获取书籍信息
        let book_repo = BookRepository::new(self.db_manager.pool());
        let book = book_repo.find_by_id(book_id).await?
            .ok_or_else(|| anyhow!("书籍不存在: {}", book_id))?;

        // 获取需要OCR的页面
        let ocr_repo = OcrRepository::new(self.db_manager.pool());
        let pending_pages = ocr_repo.get_pending_ocr_pages(book_id).await?;

        if pending_pages.is_empty() {
            info!("书籍{}没有需要OCR的页面", book_id);
            return Ok(Vec::new());
        }

        // 确定起始页面
        let start_from = start_page.unwrap_or(1);
        let pages_to_process: Vec<i32> = pending_pages.into_iter()
            .filter(|&page| page >= start_from)
            .collect();

        info!("需要OCR的页面数量: {}", pages_to_process.len());

        // 创建任务
        let mut task_ids = Vec::new();
        for (index, page_number) in pages_to_process.iter().enumerate() {
            let task_id = Uuid::new_v4().to_string();
            
            // 根据页面位置设置优先级
            let priority = if index < 5 {
                TaskPriority::High // 前5页高优先级
            } else if index < 20 {
                TaskPriority::Normal // 前20页正常优先级
            } else {
                TaskPriority::Low // 其他页面低优先级
            };

            let task = OcrTask {
                id: task_id.clone(),
                book_id: book_id.to_string(),
                pdf_path: pdf_path.to_string(),
                page_number: *page_number,
                language: language.to_string(),
                priority,
                created_at: chrono::Utc::now(),
                attempts: 0,
                max_attempts: 3,
            };

            self.add_task(task).await?;
            task_ids.push(task_id);
        }

        info!("已创建{}个OCR任务", task_ids.len());
        Ok(task_ids)
    }

    /// 获取后台状态
    pub async fn get_status(&self) -> Result<BackgroundStatus> {
        if let Some(tx) = &self.control_tx {
            let (response_tx, response_rx) = oneshot::channel();
            tx.send(ControlMessage::GetStatus(response_tx))?;
            let status = response_rx.await?;
            Ok(status)
        } else {
            Ok(BackgroundStatus {
                is_running: false,
                is_paused: false,
                queue_size: 0,
                running_tasks: 0,
                completed_tasks: 0,
                failed_tasks: 0,
                total_processing_time: Duration::ZERO,
            })
        }
    }

    /// 暂停后台服务
    pub async fn pause(&self) -> Result<()> {
        if let Some(tx) = &self.control_tx {
            tx.send(ControlMessage::Pause)?;
        }
        Ok(())
    }

    /// 恢复后台服务
    pub async fn resume(&self) -> Result<()> {
        if let Some(tx) = &self.control_tx {
            tx.send(ControlMessage::Resume)?;
        }
        Ok(())
    }

    /// 生成工作线程
    fn spawn_worker(
        worker_id: usize,
        task_queue: Arc<Mutex<VecDeque<OcrTask>>>,
        running_tasks: Arc<RwLock<HashMap<String, OcrTask>>>,
        task_results: Arc<RwLock<HashMap<String, TaskResult>>>,
        ocr_processor: Arc<OcrProcessor>,
        config: BackgroundOcrConfig,
    ) -> tokio::task::JoinHandle<()> {
        tokio::spawn(async move {
            info!("OCR工作线程{}已启动", worker_id);

            loop {
                // 从队列获取任务
                let task = {
                    let mut queue = task_queue.lock().await;
                    queue.pop_front()
                };

                if let Some(mut task) = task {
                    let start_time = Instant::now();
                    
                    // 标记任务为运行中
                    {
                        let mut running = running_tasks.write().await;
                        running.insert(task.id.clone(), task.clone());
                    }

                    debug!("工作线程{}开始处理任务: {}", worker_id, task.id);

                    // 执行OCR处理
                    let result = tokio::time::timeout(
                        config.task_timeout,
                        ocr_processor.process_page(
                            &task.book_id,
                            &task.pdf_path,
                            task.page_number,
                            Some(&task.language),
                        ),
                    ).await;

                    let processing_time = start_time.elapsed();
                    let task_result = match result {
                        Ok(Ok(_)) => {
                            info!("任务{}完成，耗时: {:?}", task.id, processing_time);
                            TaskResult {
                                task_id: task.id.clone(),
                                status: TaskStatus::Completed,
                                processing_time,
                                error_message: None,
                            }
                        }
                        Ok(Err(e)) => {
                            warn!("任务{}失败: {}", task.id, e);
                            task.attempts += 1;
                            
                            if task.attempts < task.max_attempts {
                                // 重新加入队列
                                sleep(config.retry_delay).await;
                                let mut queue = task_queue.lock().await;
                                queue.push_back(task.clone());
                                info!("任务{}重试，第{}次尝试", task.id, task.attempts);
                            }

                            TaskResult {
                                task_id: task.id.clone(),
                                status: TaskStatus::Failed(e.to_string()),
                                processing_time,
                                error_message: Some(e.to_string()),
                            }
                        }
                        Err(_) => {
                            warn!("任务{}超时", task.id);
                            TaskResult {
                                task_id: task.id.clone(),
                                status: TaskStatus::Failed("任务超时".to_string()),
                                processing_time,
                                error_message: Some("任务超时".to_string()),
                            }
                        }
                    };

                    // 移除运行中的任务
                    {
                        let mut running = running_tasks.write().await;
                        running.remove(&task.id);
                    }

                    // 保存任务结果
                    {
                        let mut results = task_results.write().await;
                        results.insert(task.id.clone(), task_result);
                    }
                } else {
                    // 队列为空，等待一段时间
                    sleep(Duration::from_millis(1000)).await;
                }
            }
        })
    }
}

/// 智能续传管理器
pub struct OcrResumeManager {
    db_manager: Arc<DatabaseManager>,
}

impl OcrResumeManager {
    pub fn new(db_manager: Arc<DatabaseManager>) -> Self {
        Self { db_manager }
    }

    /// 恢复书籍的OCR进度
    pub async fn resume_book_ocr(&self, book_id: &str) -> Result<i32> {
        let ocr_repo = OcrRepository::new(self.db_manager.pool());
        
        // 获取已完成OCR的最大页码
        let last_completed_page = sqlx::query!(
            r#"
            SELECT MAX(page_number) as max_page
            FROM pages 
            WHERE book_id = ? AND has_ocr_data = TRUE
            "#,
            book_id
        )
        .fetch_one(self.db_manager.pool())
        .await?;

        let resume_from = last_completed_page.max_page.unwrap_or(0) + 1;
        
        info!("书籍{}的OCR将从第{}页继续", book_id, resume_from);
        Ok(resume_from)
    }

    /// 保存OCR进度检查点
    pub async fn save_checkpoint(&self, book_id: &str, page_number: i32) -> Result<()> {
        // 更新书籍的OCR进度
        let book_repo = BookRepository::new(self.db_manager.pool());
        let ocr_repo = OcrRepository::new(self.db_manager.pool());
        
        let progress = ocr_repo.get_ocr_progress(book_id).await?;
        book_repo.update_ocr_progress(book_id, progress).await?;
        
        debug!("保存OCR检查点：书籍={}, 页面={}, 进度={:.1}%", book_id, page_number, progress * 100.0);
        Ok(())
    }
}
```

---

## 📱 Flutter后台服务集成

### lib/services/background_ocr_service.dart
```dart
/// 后台OCR服务
/// 
/// 管理后台OCR识别任务

import 'package:flutter/foundation.dart';
import 'package:smart_pdf_reader/bridge/rust_bridge.dart';
import 'package:smart_pdf_reader/models/ocr_models.dart';

class BackgroundOcrService {
  static final BackgroundOcrService _instance = BackgroundOcrService._internal();
  factory BackgroundOcrService() => _instance;
  BackgroundOcrService._internal();

  bool _isInitialized = false;
  bool _isRunning = false;

  /// 初始化后台OCR服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await RustBridge.api.initializeBackgroundOcr();
      _isInitialized = true;
      debugPrint('后台OCR服务初始化成功');
    } catch (e) {
      debugPrint('后台OCR服务初始化失败: $e');
      rethrow;
    }
  }

  /// 启动后台OCR服务
  Future<void> start() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      await RustBridge.api.startBackgroundOcr();
      _isRunning = true;
      debugPrint('后台OCR服务已启动');
    } catch (e) {
      debugPrint('启动后台OCR服务失败: $e');
      rethrow;
    }
  }

  /// 停止后台OCR服务
  Future<void> stop() async {
    try {
      await RustBridge.api.stopBackgroundOcr();
      _isRunning = false;
      debugPrint('后台OCR服务已停止');
    } catch (e) {
      debugPrint('停止后台OCR服务失败: $e');
      rethrow;
    }
  }

  /// 为书籍启动OCR识别
  Future<List<String>> startBookOcr({
    required String bookId,
    required String pdfPath,
    String language = 'eng',
    int? startPage,
  }) async {
    try {
      final taskIds = await RustBridge.api.createBookOcrTasks(
        bookId: bookId,
        pdfPath: pdfPath,
        language: language,
        startPage: startPage,
      );

      debugPrint('为书籍$bookId创建了${taskIds.length}个OCR任务');
      return taskIds;
    } catch (e) {
      debugPrint('启动书籍OCR失败: $e');
      rethrow;
    }
  }

  /// 获取后台OCR状态
  Future<BackgroundOcrStatus> getStatus() async {
    try {
      final status = await RustBridge.api.getBackgroundOcrStatus();
      return BackgroundOcrStatus.fromRust(status);
    } catch (e) {
      debugPrint('获取后台OCR状态失败: $e');
      return BackgroundOcrStatus.empty();
    }
  }

  /// 暂停后台OCR
  Future<void> pause() async {
    try {
      await RustBridge.api.pauseBackgroundOcr();
      debugPrint('后台OCR已暂停');
    } catch (e) {
      debugPrint('暂停后台OCR失败: $e');
      rethrow;
    }
  }

  /// 恢复后台OCR
  Future<void> resume() async {
    try {
      await RustBridge.api.resumeBackgroundOcr();
      debugPrint('后台OCR已恢复');
    } catch (e) {
      debugPrint('恢复后台OCR失败: $e');
      rethrow;
    }
  }

  /// 取消任务
  Future<void> cancelTask(String taskId) async {
    try {
      await RustBridge.api.cancelOcrTask(taskId: taskId);
      debugPrint('已取消OCR任务: $taskId');
    } catch (e) {
      debugPrint('取消OCR任务失败: $e');
      rethrow;
    }
  }

  /// 获取书籍OCR进度
  Future<double> getBookOcrProgress(String bookId) async {
    try {
      return await RustBridge.api.getBookOcrProgress(bookId: bookId);
    } catch (e) {
      debugPrint('获取书籍OCR进度失败: $e');
      return 0.0;
    }
  }

  /// 检查服务是否运行
  bool get isRunning => _isRunning;

  /// 检查服务是否初始化
  bool get isInitialized => _isInitialized;
}

/// 后台OCR状态
class BackgroundOcrStatus {
  final bool isRunning;
  final bool isPaused;
  final int queueSize;
  final int runningTasks;
  final int completedTasks;
  final int failedTasks;
  final Duration totalProcessingTime;

  const BackgroundOcrStatus({
    required this.isRunning,
    required this.isPaused,
    required this.queueSize,
    required this.runningTasks,
    required this.completedTasks,
    required this.failedTasks,
    required this.totalProcessingTime,
  });

  factory BackgroundOcrStatus.fromRust(dynamic rustStatus) {
    return BackgroundOcrStatus(
      isRunning: rustStatus.isRunning,
      isPaused: rustStatus.isPaused,
      queueSize: rustStatus.queueSize,
      runningTasks: rustStatus.runningTasks,
      completedTasks: rustStatus.completedTasks,
      failedTasks: rustStatus.failedTasks,
      totalProcessingTime: Duration(
        microseconds: (rustStatus.totalProcessingTime * 1000000).round(),
      ),
    );
  }

  factory BackgroundOcrStatus.empty() {
    return const BackgroundOcrStatus(
      isRunning: false,
      isPaused: false,
      queueSize: 0,
      runningTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      totalProcessingTime: Duration.zero,
    );
  }

  /// 总任务数
  int get totalTasks => completedTasks + failedTasks + queueSize + runningTasks;

  /// 完成率
  double get completionRate {
    if (totalTasks == 0) return 0.0;
    return completedTasks / totalTasks;
  }

  /// 成功率
  double get successRate {
    final processedTasks = completedTasks + failedTasks;
    if (processedTasks == 0) return 0.0;
    return completedTasks / processedTasks;
  }

  /// 是否有活动
  bool get hasActivity => isRunning && (queueSize > 0 || runningTasks > 0);
}
```

---

## ✅ 后台识别机制完成

后台OCR识别机制已经完成，包括：

1. **后台任务管理** - 任务队列、优先级、并发控制
2. **智能续传机制** - 断点续传、进度保存、状态恢复
3. **资源管理** - 超时控制、重试机制、错误处理
4. **Flutter集成** - 后台服务的高级封装和状态管理
5. **性能优化** - 并发限制、内存管理、电池优化

核心特性：
- **持续识别**：后台持续处理OCR任务，不影响用户阅读
- **智能续传**：应用重启后从上次进度继续识别
- **优先级调度**：前几页高优先级，确保用户体验
- **资源控制**：限制并发数和内存使用，避免设备过热

下一步可以继续进行：
- **重排显示系统** - 基于OCR数据的重排视图
- **对照编辑功能** - 分屏对照和文字编辑
- **TTS语音系统** - 语音阅读功能集成

---

*后台识别机制版本: v1.0*  
*支持功能: 持续识别、智能续传、任务管理*
