# 🗄️ 智能PDF阅读器数据库架构设计

## 🏗️ 三层数据库架构概述

```
┌─────────────────────────────────────┐
│  重排显示层（Flutter UI）              │
│  - 动态重排布局                       │
│  - 用户阅读界面                       │
│  - 不影响数据库                       │
├─────────────────────────────────────┤
│  OCR数据库层（Rust Backend）           │
│  - 保持原始位置坐标                    │
│  - 支持内容编辑                       │
│  - 位置关系不变                       │
├─────────────────────────────────────┤
│  原始PDF层（File System）             │
│  - 不可变原始文档                     │
│  - 坐标参考基准                       │
│  - 永久保存                          │
└─────────────────────────────────────┘
```

---

## 📊 数据库表结构设计

### 1. 书籍信息表 (books)
```sql
CREATE TABLE books (
    id TEXT PRIMARY KEY,                    -- 书籍唯一标识
    title TEXT NOT NULL,                    -- 书籍标题
    author TEXT,                            -- 作者
    file_path TEXT NOT NULL UNIQUE,         -- 文件路径
    cover_path TEXT,                        -- 封面图片路径
    page_count INTEGER NOT NULL,            -- 总页数
    file_size INTEGER NOT NULL,             -- 文件大小(字节)
    file_hash TEXT NOT NULL,                -- 文件哈希值
    is_scanned BOOLEAN DEFAULT FALSE,       -- 是否为扫描版
    has_ocr_data BOOLEAN DEFAULT FALSE,     -- 是否有OCR数据
    ocr_progress REAL DEFAULT 0.0,          -- OCR识别进度(0-1)
    ocr_engine TEXT,                        -- 使用的OCR引擎
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_books_title ON books(title);
CREATE INDEX idx_books_author ON books(author);
CREATE INDEX idx_books_created_at ON books(created_at);
```

### 2. 页面信息表 (pages)
```sql
CREATE TABLE pages (
    id TEXT PRIMARY KEY,                    -- 页面唯一标识
    book_id TEXT NOT NULL,                  -- 所属书籍ID
    page_number INTEGER NOT NULL,           -- 物理页码
    content_page_number INTEGER,            -- 内容页码(可能为空)
    width REAL NOT NULL,                    -- 页面宽度
    height REAL NOT NULL,                   -- 页面高度
    has_ocr_data BOOLEAN DEFAULT FALSE,     -- 是否有OCR数据
    ocr_confidence REAL,                    -- OCR识别置信度
    ocr_processing_time REAL,               -- OCR处理时间(秒)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- 索引
CREATE UNIQUE INDEX idx_pages_book_page ON pages(book_id, page_number);
CREATE INDEX idx_pages_content_page ON pages(book_id, content_page_number);
```

### 3. OCR文本块表 (text_blocks)
```sql
CREATE TABLE text_blocks (
    id TEXT PRIMARY KEY,                    -- 文本块唯一标识
    page_id TEXT NOT NULL,                  -- 所属页面ID
    original_text TEXT NOT NULL,            -- 原始OCR识别文本
    edited_text TEXT,                       -- 用户编辑后的文本
    x REAL NOT NULL,                        -- X坐标
    y REAL NOT NULL,                        -- Y坐标
    width REAL NOT NULL,                    -- 宽度
    height REAL NOT NULL,                   -- 高度
    confidence REAL NOT NULL,               -- 识别置信度
    font_size REAL,                         -- 字体大小
    is_title BOOLEAN DEFAULT FALSE,         -- 是否为标题
    is_deleted BOOLEAN DEFAULT FALSE,       -- 是否被用户删除
    block_order INTEGER NOT NULL,           -- 在页面中的顺序
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_text_blocks_page ON text_blocks(page_id);
CREATE INDEX idx_text_blocks_order ON text_blocks(page_id, block_order);
CREATE INDEX idx_text_blocks_position ON text_blocks(page_id, x, y);
```

### 4. 文本编辑历史表 (text_edit_history)
```sql
CREATE TABLE text_edit_history (
    id TEXT PRIMARY KEY,                    -- 历史记录唯一标识
    text_block_id TEXT NOT NULL,           -- 文本块ID
    old_text TEXT NOT NULL,                -- 修改前文本
    new_text TEXT NOT NULL,                -- 修改后文本
    edit_type TEXT NOT NULL,               -- 编辑类型: 'update', 'delete', 'restore'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (text_block_id) REFERENCES text_blocks(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_edit_history_block ON text_edit_history(text_block_id);
CREATE INDEX idx_edit_history_time ON text_edit_history(created_at);
```

### 5. 阅读进度表 (reading_progress)
```sql
CREATE TABLE reading_progress (
    id TEXT PRIMARY KEY,                    -- 进度记录唯一标识
    book_id TEXT NOT NULL,                  -- 书籍ID
    current_page INTEGER NOT NULL,          -- 当前页码
    scroll_position REAL DEFAULT 0.0,       -- 滚动位置
    reading_mode TEXT DEFAULT 'original',   -- 阅读模式: 'original', 'reflow', 'comparison'
    total_reading_time INTEGER DEFAULT 0,   -- 总阅读时间(秒)
    last_read_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- 索引
CREATE UNIQUE INDEX idx_reading_progress_book ON reading_progress(book_id);
```

### 6. 书签表 (bookmarks)
```sql
CREATE TABLE bookmarks (
    id TEXT PRIMARY KEY,                    -- 书签唯一标识
    book_id TEXT NOT NULL,                  -- 书籍ID
    page_number INTEGER NOT NULL,           -- 页码
    title TEXT NOT NULL,                    -- 书签标题
    note TEXT,                              -- 书签备注
    x REAL,                                 -- X坐标(可选)
    y REAL,                                 -- Y坐标(可选)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_bookmarks_book ON bookmarks(book_id);
CREATE INDEX idx_bookmarks_page ON bookmarks(book_id, page_number);
```

### 7. 高亮和批注表 (annotations)
```sql
CREATE TABLE annotations (
    id TEXT PRIMARY KEY,                    -- 批注唯一标识
    book_id TEXT NOT NULL,                  -- 书籍ID
    page_number INTEGER NOT NULL,           -- 页码
    annotation_type TEXT NOT NULL,          -- 类型: 'highlight', 'note', 'underline'
    text_content TEXT,                      -- 选中的文本内容
    note_content TEXT,                      -- 批注内容
    color TEXT DEFAULT '#FFFF00',           -- 颜色
    x REAL NOT NULL,                        -- X坐标
    y REAL NOT NULL,                        -- Y坐标
    width REAL NOT NULL,                    -- 宽度
    height REAL NOT NULL,                   -- 高度
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_annotations_book ON annotations(book_id);
CREATE INDEX idx_annotations_page ON annotations(book_id, page_number);
CREATE INDEX idx_annotations_type ON annotations(annotation_type);
```

### 8. OCR引擎配置表 (ocr_engines)
```sql
CREATE TABLE ocr_engines (
    id TEXT PRIMARY KEY,                    -- 引擎唯一标识
    name TEXT NOT NULL,                     -- 引擎名称
    version TEXT NOT NULL,                  -- 版本
    is_enabled BOOLEAN DEFAULT TRUE,        -- 是否启用
    is_default BOOLEAN DEFAULT FALSE,       -- 是否为默认引擎
    config_json TEXT,                       -- 配置JSON
    performance_score REAL DEFAULT 0.0,     -- 性能评分
    accuracy_score REAL DEFAULT 0.0,        -- 准确率评分
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE UNIQUE INDEX idx_ocr_engines_name_version ON ocr_engines(name, version);
```

---

## 🦀 Rust数据库实现

### rust_core/src/database/mod.rs
```rust
//! 数据库管理模块
//! 
//! 实现三层数据库架构的核心逻辑

pub mod models;
pub mod migrations;
pub mod repositories;

use sqlx::{SqlitePool, sqlite::SqlitePoolOptions};
use anyhow::Result;
use std::path::Path;

/// 数据库管理器
pub struct DatabaseManager {
    pool: SqlitePool,
}

impl DatabaseManager {
    /// 创建新的数据库管理器
    pub async fn new(db_path: &str) -> Result<Self> {
        // 确保数据库目录存在
        if let Some(parent) = Path::new(db_path).parent() {
            tokio::fs::create_dir_all(parent).await?;
        }

        // 创建连接池
        let pool = SqlitePoolOptions::new()
            .max_connections(10)
            .connect(&format!("sqlite:{}", db_path))
            .await?;

        let manager = Self { pool };
        
        // 运行数据库迁移
        manager.run_migrations().await?;
        
        Ok(manager)
    }

    /// 获取数据库连接池
    pub fn pool(&self) -> &SqlitePool {
        &self.pool
    }

    /// 运行数据库迁移
    async fn run_migrations(&self) -> Result<()> {
        tracing::info!("开始运行数据库迁移...");
        
        // 创建迁移表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS schema_migrations (
                version TEXT PRIMARY KEY,
                applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            "#
        )
        .execute(&self.pool)
        .await?;

        // 运行所有迁移
        migrations::run_all_migrations(&self.pool).await?;
        
        tracing::info!("数据库迁移完成");
        Ok(())
    }

    /// 关闭数据库连接
    pub async fn close(self) {
        self.pool.close().await;
    }
}
```

### rust_core/src/database/models.rs
```rust
//! 数据库模型定义

use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc};

/// 书籍信息模型
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Book {
    pub id: String,
    pub title: String,
    pub author: Option<String>,
    pub file_path: String,
    pub cover_path: Option<String>,
    pub page_count: i32,
    pub file_size: i64,
    pub file_hash: String,
    pub is_scanned: bool,
    pub has_ocr_data: bool,
    pub ocr_progress: f64,
    pub ocr_engine: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 页面信息模型
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Page {
    pub id: String,
    pub book_id: String,
    pub page_number: i32,
    pub content_page_number: Option<i32>,
    pub width: f64,
    pub height: f64,
    pub has_ocr_data: bool,
    pub ocr_confidence: Option<f64>,
    pub ocr_processing_time: Option<f64>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// OCR文本块模型
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct TextBlock {
    pub id: String,
    pub page_id: String,
    pub original_text: String,
    pub edited_text: Option<String>,
    pub x: f64,
    pub y: f64,
    pub width: f64,
    pub height: f64,
    pub confidence: f64,
    pub font_size: Option<f64>,
    pub is_title: bool,
    pub is_deleted: bool,
    pub block_order: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl TextBlock {
    /// 获取当前显示的文本（优先使用编辑后的文本）
    pub fn display_text(&self) -> &str {
        self.edited_text.as_ref().unwrap_or(&self.original_text)
    }

    /// 检查文本是否被用户修改过
    pub fn is_edited(&self) -> bool {
        self.edited_text.is_some()
    }
}

/// 文本编辑历史模型
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct TextEditHistory {
    pub id: String,
    pub text_block_id: String,
    pub old_text: String,
    pub new_text: String,
    pub edit_type: String,
    pub created_at: DateTime<Utc>,
}

/// 阅读进度模型
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ReadingProgress {
    pub id: String,
    pub book_id: String,
    pub current_page: i32,
    pub scroll_position: f64,
    pub reading_mode: String,
    pub total_reading_time: i32,
    pub last_read_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 书签模型
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Bookmark {
    pub id: String,
    pub book_id: String,
    pub page_number: i32,
    pub title: String,
    pub note: Option<String>,
    pub x: Option<f64>,
    pub y: Option<f64>,
    pub created_at: DateTime<Utc>,
}

/// 批注模型
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Annotation {
    pub id: String,
    pub book_id: String,
    pub page_number: i32,
    pub annotation_type: String,
    pub text_content: Option<String>,
    pub note_content: Option<String>,
    pub color: String,
    pub x: f64,
    pub y: f64,
    pub width: f64,
    pub height: f64,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// OCR引擎配置模型
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct OcrEngine {
    pub id: String,
    pub name: String,
    pub version: String,
    pub is_enabled: bool,
    pub is_default: bool,
    pub config_json: Option<String>,
    pub performance_score: f64,
    pub accuracy_score: f64,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
```

### rust_core/src/database/repositories/book_repository.rs
```rust
//! 书籍数据仓库

use crate::database::models::Book;
use sqlx::SqlitePool;
use anyhow::Result;
use uuid::Uuid;
use chrono::Utc;

/// 书籍数据仓库
pub struct BookRepository<'a> {
    pool: &'a SqlitePool,
}

impl<'a> BookRepository<'a> {
    pub fn new(pool: &'a SqlitePool) -> Self {
        Self { pool }
    }

    /// 创建新书籍
    pub async fn create(&self, book: &Book) -> Result<()> {
        sqlx::query!(
            r#"
            INSERT INTO books (
                id, title, author, file_path, cover_path, page_count,
                file_size, file_hash, is_scanned, has_ocr_data,
                ocr_progress, ocr_engine, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
            book.id,
            book.title,
            book.author,
            book.file_path,
            book.cover_path,
            book.page_count,
            book.file_size,
            book.file_hash,
            book.is_scanned,
            book.has_ocr_data,
            book.ocr_progress,
            book.ocr_engine,
            book.created_at,
            book.updated_at
        )
        .execute(self.pool)
        .await?;

        Ok(())
    }

    /// 根据ID查找书籍
    pub async fn find_by_id(&self, id: &str) -> Result<Option<Book>> {
        let book = sqlx::query_as!(
            Book,
            "SELECT * FROM books WHERE id = ?",
            id
        )
        .fetch_optional(self.pool)
        .await?;

        Ok(book)
    }

    /// 获取所有书籍
    pub async fn find_all(&self) -> Result<Vec<Book>> {
        let books = sqlx::query_as!(
            Book,
            "SELECT * FROM books ORDER BY created_at DESC"
        )
        .fetch_all(self.pool)
        .await?;

        Ok(books)
    }

    /// 更新书籍OCR进度
    pub async fn update_ocr_progress(&self, id: &str, progress: f64) -> Result<()> {
        sqlx::query!(
            "UPDATE books SET ocr_progress = ?, updated_at = ? WHERE id = ?",
            progress,
            Utc::now(),
            id
        )
        .execute(self.pool)
        .await?;

        Ok(())
    }

    /// 标记书籍有OCR数据
    pub async fn mark_has_ocr_data(&self, id: &str) -> Result<()> {
        sqlx::query!(
            "UPDATE books SET has_ocr_data = TRUE, updated_at = ? WHERE id = ?",
            Utc::now(),
            id
        )
        .execute(self.pool)
        .await?;

        Ok(())
    }

    /// 删除书籍
    pub async fn delete(&self, id: &str) -> Result<()> {
        sqlx::query!("DELETE FROM books WHERE id = ?", id)
            .execute(self.pool)
            .await?;

        Ok(())
    }
}
```

### rust_core/src/database/repositories/text_block_repository.rs
```rust
//! 文本块数据仓库

use crate::database::models::{TextBlock, TextEditHistory};
use sqlx::SqlitePool;
use anyhow::Result;
use uuid::Uuid;
use chrono::Utc;

/// 文本块数据仓库
pub struct TextBlockRepository<'a> {
    pool: &'a SqlitePool,
}

impl<'a> TextBlockRepository<'a> {
    pub fn new(pool: &'a SqlitePool) -> Self {
        Self { pool }
    }

    /// 批量创建文本块
    pub async fn create_batch(&self, text_blocks: &[TextBlock]) -> Result<()> {
        let mut tx = self.pool.begin().await?;

        for block in text_blocks {
            sqlx::query!(
                r#"
                INSERT INTO text_blocks (
                    id, page_id, original_text, edited_text, x, y, width, height,
                    confidence, font_size, is_title, is_deleted, block_order,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                "#,
                block.id,
                block.page_id,
                block.original_text,
                block.edited_text,
                block.x,
                block.y,
                block.width,
                block.height,
                block.confidence,
                block.font_size,
                block.is_title,
                block.is_deleted,
                block.block_order,
                block.created_at,
                block.updated_at
            )
            .execute(&mut *tx)
            .await?;
        }

        tx.commit().await?;
        Ok(())
    }

    /// 获取页面的所有文本块
    pub async fn find_by_page_id(&self, page_id: &str) -> Result<Vec<TextBlock>> {
        let blocks = sqlx::query_as!(
            TextBlock,
            "SELECT * FROM text_blocks WHERE page_id = ? ORDER BY block_order",
            page_id
        )
        .fetch_all(self.pool)
        .await?;

        Ok(blocks)
    }

    /// 更新文本块内容
    pub async fn update_text(&self, id: &str, new_text: &str) -> Result<()> {
        // 先获取原始文本用于历史记录
        let old_block = sqlx::query_as!(
            TextBlock,
            "SELECT * FROM text_blocks WHERE id = ?",
            id
        )
        .fetch_one(self.pool)
        .await?;

        let mut tx = self.pool.begin().await?;

        // 更新文本块
        sqlx::query!(
            "UPDATE text_blocks SET edited_text = ?, updated_at = ? WHERE id = ?",
            new_text,
            Utc::now(),
            id
        )
        .execute(&mut *tx)
        .await?;

        // 记录编辑历史
        let history = TextEditHistory {
            id: Uuid::new_v4().to_string(),
            text_block_id: id.to_string(),
            old_text: old_block.display_text().to_string(),
            new_text: new_text.to_string(),
            edit_type: "update".to_string(),
            created_at: Utc::now(),
        };

        sqlx::query!(
            r#"
            INSERT INTO text_edit_history (
                id, text_block_id, old_text, new_text, edit_type, created_at
            ) VALUES (?, ?, ?, ?, ?, ?)
            "#,
            history.id,
            history.text_block_id,
            history.old_text,
            history.new_text,
            history.edit_type,
            history.created_at
        )
        .execute(&mut *tx)
        .await?;

        tx.commit().await?;
        Ok(())
    }

    /// 标记文本块为删除
    pub async fn mark_deleted(&self, id: &str) -> Result<()> {
        sqlx::query!(
            "UPDATE text_blocks SET is_deleted = TRUE, updated_at = ? WHERE id = ?",
            Utc::now(),
            id
        )
        .execute(self.pool)
        .await?;

        Ok(())
    }

    /// 获取文本块的编辑历史
    pub async fn get_edit_history(&self, text_block_id: &str) -> Result<Vec<TextEditHistory>> {
        let history = sqlx::query_as!(
            TextEditHistory,
            "SELECT * FROM text_edit_history WHERE text_block_id = ? ORDER BY created_at DESC",
            text_block_id
        )
        .fetch_all(self.pool)
        .await?;

        Ok(history)
    }
}
```

### rust_core/src/database/repositories/ocr_repository.rs
```rust
//! OCR数据仓库
//!
//! 专门处理OCR识别结果的存储和查询

use crate::database::models::{Page, TextBlock, TextEditHistory};
use crate::ocr::{OcrResult, OcrTextBlock};
use sqlx::SqlitePool;
use anyhow::Result;
use uuid::Uuid;
use chrono::Utc;
use tracing::{info, warn, error};

/// OCR数据仓库
pub struct OcrRepository<'a> {
    pool: &'a SqlitePool,
}

impl<'a> OcrRepository<'a> {
    pub fn new(pool: &'a SqlitePool) -> Self {
        Self { pool }
    }

    /// 保存OCR识别结果到数据库
    ///
    /// 这是核心功能：将OCR结果按原始位置存储，保持坐标不变
    pub async fn save_ocr_result(&self, book_id: &str, ocr_result: &OcrResult) -> Result<()> {
        let mut tx = self.pool.begin().await?;

        // 1. 创建或更新页面记录
        let page_id = Uuid::new_v4().to_string();
        let page = Page {
            id: page_id.clone(),
            book_id: book_id.to_string(),
            page_number: ocr_result.page_number,
            content_page_number: None, // 稍后通过页码映射设置
            width: 0.0,  // TODO: 从PDF获取实际页面尺寸
            height: 0.0, // TODO: 从PDF获取实际页面尺寸
            has_ocr_data: true,
            ocr_confidence: Some(ocr_result.confidence as f64),
            ocr_processing_time: Some(ocr_result.processing_time as f64),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        // 检查页面是否已存在
        let existing_page = sqlx::query!(
            "SELECT id FROM pages WHERE book_id = ? AND page_number = ?",
            book_id,
            ocr_result.page_number
        )
        .fetch_optional(&mut *tx)
        .await?;

        let final_page_id = if let Some(existing) = existing_page {
            // 更新现有页面
            sqlx::query!(
                r#"
                UPDATE pages SET
                    has_ocr_data = TRUE,
                    ocr_confidence = ?,
                    ocr_processing_time = ?,
                    updated_at = ?
                WHERE id = ?
                "#,
                page.ocr_confidence,
                page.ocr_processing_time,
                page.updated_at,
                existing.id
            )
            .execute(&mut *tx)
            .await?;

            existing.id
        } else {
            // 创建新页面
            sqlx::query!(
                r#"
                INSERT INTO pages (
                    id, book_id, page_number, content_page_number, width, height,
                    has_ocr_data, ocr_confidence, ocr_processing_time,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                "#,
                page.id,
                page.book_id,
                page.page_number,
                page.content_page_number,
                page.width,
                page.height,
                page.has_ocr_data,
                page.ocr_confidence,
                page.ocr_processing_time,
                page.created_at,
                page.updated_at
            )
            .execute(&mut *tx)
            .await?;

            page.id
        };

        // 2. 删除该页面的旧文本块（如果重新识别）
        sqlx::query!(
            "DELETE FROM text_blocks WHERE page_id = ?",
            final_page_id
        )
        .execute(&mut *tx)
        .await?;

        // 3. 保存新的文本块，严格保持原始位置坐标
        for ocr_block in &ocr_result.text_blocks {
            let text_block = TextBlock {
                id: Uuid::new_v4().to_string(),
                page_id: final_page_id.clone(),
                original_text: ocr_block.text.clone(),
                edited_text: None, // 初始时没有编辑
                x: ocr_block.x as f64,
                y: ocr_block.y as f64,
                width: ocr_block.width as f64,
                height: ocr_block.height as f64,
                confidence: ocr_block.confidence as f64,
                font_size: ocr_block.font_size.map(|f| f as f64),
                is_title: ocr_block.is_title,
                is_deleted: false,
                block_order: ocr_block.block_order,
                created_at: Utc::now(),
                updated_at: Utc::now(),
            };

            sqlx::query!(
                r#"
                INSERT INTO text_blocks (
                    id, page_id, original_text, edited_text, x, y, width, height,
                    confidence, font_size, is_title, is_deleted, block_order,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                "#,
                text_block.id,
                text_block.page_id,
                text_block.original_text,
                text_block.edited_text,
                text_block.x,
                text_block.y,
                text_block.width,
                text_block.height,
                text_block.confidence,
                text_block.font_size,
                text_block.is_title,
                text_block.is_deleted,
                text_block.block_order,
                text_block.created_at,
                text_block.updated_at
            )
            .execute(&mut *tx)
            .await?;
        }

        tx.commit().await?;

        info!(
            "OCR结果已保存：书籍ID={}, 页面={}, 文本块数量={}",
            book_id,
            ocr_result.page_number,
            ocr_result.text_blocks.len()
        );

        Ok(())
    }

    /// 获取页面的OCR文本块（用于重排显示）
    ///
    /// 返回的文本块保持原始位置坐标，但可能包含用户编辑的内容
    pub async fn get_page_text_blocks(&self, book_id: &str, page_number: i32) -> Result<Vec<TextBlock>> {
        let blocks = sqlx::query_as!(
            TextBlock,
            r#"
            SELECT tb.* FROM text_blocks tb
            JOIN pages p ON tb.page_id = p.id
            WHERE p.book_id = ? AND p.page_number = ?
            AND tb.is_deleted = FALSE
            ORDER BY tb.block_order
            "#,
            book_id,
            page_number
        )
        .fetch_all(self.pool)
        .await?;

        Ok(blocks)
    }

    /// 获取页面的重排文本（用于重排显示）
    ///
    /// 返回处理后的文本，但不改变数据库中的位置信息
    pub async fn get_page_reflow_text(&self, book_id: &str, page_number: i32) -> Result<String> {
        let blocks = self.get_page_text_blocks(book_id, page_number).await?;

        let mut reflow_text = String::new();

        for block in blocks {
            // 使用编辑后的文本（如果有）或原始文本
            let text = block.edited_text.as_ref().unwrap_or(&block.original_text);

            if block.is_title {
                reflow_text.push_str(&format!("\n# {}\n\n", text));
            } else {
                reflow_text.push_str(&format!("{}\n", text));
            }
        }

        Ok(reflow_text.trim().to_string())
    }

    /// 更新文本块内容（用户编辑）
    ///
    /// 关键：只更新文本内容，位置坐标保持不变
    pub async fn update_text_block_content(&self, block_id: &str, new_text: &str) -> Result<()> {
        // 获取原始文本用于历史记录
        let original_block = sqlx::query_as!(
            TextBlock,
            "SELECT * FROM text_blocks WHERE id = ?",
            block_id
        )
        .fetch_one(self.pool)
        .await?;

        let mut tx = self.pool.begin().await?;

        // 更新文本内容，但保持位置坐标不变
        sqlx::query!(
            r#"
            UPDATE text_blocks
            SET edited_text = ?, updated_at = ?
            WHERE id = ?
            "#,
            new_text,
            Utc::now(),
            block_id
        )
        .execute(&mut *tx)
        .await?;

        // 记录编辑历史
        let history = TextEditHistory {
            id: Uuid::new_v4().to_string(),
            text_block_id: block_id.to_string(),
            old_text: original_block.display_text().to_string(),
            new_text: new_text.to_string(),
            edit_type: "update".to_string(),
            created_at: Utc::now(),
        };

        sqlx::query!(
            r#"
            INSERT INTO text_edit_history (
                id, text_block_id, old_text, new_text, edit_type, created_at
            ) VALUES (?, ?, ?, ?, ?, ?)
            "#,
            history.id,
            history.text_block_id,
            history.old_text,
            history.new_text,
            history.edit_type,
            history.created_at
        )
        .execute(&mut *tx)
        .await?;

        tx.commit().await?;

        info!("文本块已更新：ID={}, 新内容长度={}", block_id, new_text.len());
        Ok(())
    }

    /// 删除文本块（标记为删除，保持位置坐标）
    ///
    /// 关键：不真正删除，只标记为删除，位置坐标保留
    pub async fn delete_text_block(&self, block_id: &str) -> Result<()> {
        let original_block = sqlx::query_as!(
            TextBlock,
            "SELECT * FROM text_blocks WHERE id = ?",
            block_id
        )
        .fetch_one(self.pool)
        .await?;

        let mut tx = self.pool.begin().await?;

        // 标记为删除，但保持所有位置信息
        sqlx::query!(
            r#"
            UPDATE text_blocks
            SET is_deleted = TRUE, updated_at = ?
            WHERE id = ?
            "#,
            Utc::now(),
            block_id
        )
        .execute(&mut *tx)
        .await?;

        // 记录删除历史
        let history = TextEditHistory {
            id: Uuid::new_v4().to_string(),
            text_block_id: block_id.to_string(),
            old_text: original_block.display_text().to_string(),
            new_text: "".to_string(), // 删除后为空
            edit_type: "delete".to_string(),
            created_at: Utc::now(),
        };

        sqlx::query!(
            r#"
            INSERT INTO text_edit_history (
                id, text_block_id, old_text, new_text, edit_type, created_at
            ) VALUES (?, ?, ?, ?, ?, ?)
            "#,
            history.id,
            history.text_block_id,
            history.old_text,
            history.new_text,
            history.edit_type,
            history.created_at
        )
        .execute(&mut *tx)
        .await?;

        tx.commit().await?;

        info!("文本块已标记删除：ID={}", block_id);
        Ok(())
    }

    /// 恢复删除的文本块
    pub async fn restore_text_block(&self, block_id: &str) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE text_blocks
            SET is_deleted = FALSE, updated_at = ?
            WHERE id = ?
            "#,
            Utc::now(),
            block_id
        )
        .execute(self.pool)
        .await?;

        // 记录恢复历史
        let history = TextEditHistory {
            id: Uuid::new_v4().to_string(),
            text_block_id: block_id.to_string(),
            old_text: "".to_string(),
            new_text: "已恢复".to_string(),
            edit_type: "restore".to_string(),
            created_at: Utc::now(),
        };

        sqlx::query!(
            r#"
            INSERT INTO text_edit_history (
                id, text_block_id, old_text, new_text, edit_type, created_at
            ) VALUES (?, ?, ?, ?, ?, ?)
            "#,
            history.id,
            history.text_block_id,
            history.old_text,
            history.new_text,
            history.edit_type,
            history.created_at
        )
        .execute(self.pool)
        .await?;

        info!("文本块已恢复：ID={}", block_id);
        Ok(())
    }

    /// 获取书籍的OCR进度
    pub async fn get_ocr_progress(&self, book_id: &str) -> Result<f64> {
        let result = sqlx::query!(
            r#"
            SELECT
                COUNT(*) as total_pages,
                COUNT(CASE WHEN has_ocr_data = TRUE THEN 1 END) as ocr_pages
            FROM pages
            WHERE book_id = ?
            "#,
            book_id
        )
        .fetch_one(self.pool)
        .await?;

        if result.total_pages == 0 {
            return Ok(0.0);
        }

        let progress = result.ocr_pages as f64 / result.total_pages as f64;
        Ok(progress)
    }

    /// 获取需要OCR识别的页面列表
    pub async fn get_pending_ocr_pages(&self, book_id: &str) -> Result<Vec<i32>> {
        let pages = sqlx::query!(
            r#"
            SELECT page_number
            FROM pages
            WHERE book_id = ? AND has_ocr_data = FALSE
            ORDER BY page_number
            "#,
            book_id
        )
        .fetch_all(self.pool)
        .await?;

        Ok(pages.into_iter().map(|p| p.page_number).collect())
    }

    /// 批量创建页面记录（用于初始化）
    pub async fn create_pages_for_book(&self, book_id: &str, page_count: i32) -> Result<()> {
        let mut tx = self.pool.begin().await?;

        for page_num in 1..=page_count {
            let page_id = Uuid::new_v4().to_string();

            sqlx::query!(
                r#"
                INSERT OR IGNORE INTO pages (
                    id, book_id, page_number, content_page_number, width, height,
                    has_ocr_data, ocr_confidence, ocr_processing_time,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                "#,
                page_id,
                book_id,
                page_num,
                None::<i32>,
                0.0,
                0.0,
                false,
                None::<f64>,
                None::<f64>,
                Utc::now(),
                Utc::now()
            )
            .execute(&mut *tx)
            .await?;
        }

        tx.commit().await?;

        info!("已为书籍创建{}个页面记录：书籍ID={}", page_count, book_id);
        Ok(())
    }
}
```

### rust_core/src/core/ocr_processor.rs
```rust
//! OCR处理器
//!
//! 协调OCR识别和数据库存储的核心逻辑

use crate::database::{DatabaseManager, repositories::OcrRepository};
use crate::ocr::{OcrManager, OcrResult};
use anyhow::Result;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, warn, error};

/// OCR处理器
pub struct OcrProcessor {
    db_manager: Arc<DatabaseManager>,
    ocr_manager: Arc<RwLock<OcrManager>>,
}

impl OcrProcessor {
    /// 创建新的OCR处理器
    pub fn new(db_manager: Arc<DatabaseManager>, ocr_manager: Arc<RwLock<OcrManager>>) -> Self {
        Self {
            db_manager,
            ocr_manager,
        }
    }

    /// 处理单个页面的OCR识别和存储
    pub async fn process_page(&self, book_id: &str, pdf_path: &str, page_number: i32, language: Option<&str>) -> Result<()> {
        info!("开始处理页面OCR：书籍ID={}, 页面={}", book_id, page_number);

        // 1. 执行OCR识别
        let ocr_manager = self.ocr_manager.read().await;
        let ocr_result = ocr_manager.recognize_pdf_page(pdf_path, page_number, language).await?;
        drop(ocr_manager);

        // 2. 保存到数据库，严格保持原始位置
        let ocr_repo = OcrRepository::new(self.db_manager.pool());
        ocr_repo.save_ocr_result(book_id, &ocr_result).await?;

        // 3. 更新书籍OCR进度
        let progress = ocr_repo.get_ocr_progress(book_id).await?;
        let book_repo = crate::database::repositories::BookRepository::new(self.db_manager.pool());
        book_repo.update_ocr_progress(book_id, progress).await?;

        info!("页面OCR处理完成：书籍ID={}, 页面={}, 进度={:.1}%", book_id, page_number, progress * 100.0);
        Ok(())
    }

    /// 批量处理多个页面
    pub async fn process_pages_batch(&self, book_id: &str, pdf_path: &str, page_numbers: &[i32], language: Option<&str>) -> Result<()> {
        info!("开始批量OCR处理：书籍ID={}, 页面数量={}", book_id, page_numbers.len());

        let mut success_count = 0;
        let mut error_count = 0;

        for &page_number in page_numbers {
            match self.process_page(book_id, pdf_path, page_number, language).await {
                Ok(_) => {
                    success_count += 1;
                    info!("页面{}处理成功", page_number);
                }
                Err(e) => {
                    error_count += 1;
                    error!("页面{}处理失败: {}", page_number, e);
                }
            }
        }

        info!("批量OCR处理完成：成功={}, 失败={}", success_count, error_count);

        if success_count > 0 {
            // 标记书籍有OCR数据
            let book_repo = crate::database::repositories::BookRepository::new(self.db_manager.pool());
            book_repo.mark_has_ocr_data(book_id).await?;
        }

        Ok(())
    }

    /// 获取页面的重排文本（用于显示）
    pub async fn get_page_reflow_text(&self, book_id: &str, page_number: i32) -> Result<String> {
        let ocr_repo = OcrRepository::new(self.db_manager.pool());
        ocr_repo.get_page_reflow_text(book_id, page_number).await
    }

    /// 更新文本块内容（用户编辑）
    pub async fn update_text_block(&self, block_id: &str, new_text: &str) -> Result<()> {
        let ocr_repo = OcrRepository::new(self.db_manager.pool());
        ocr_repo.update_text_block_content(block_id, new_text).await
    }

    /// 删除文本块
    pub async fn delete_text_block(&self, block_id: &str) -> Result<()> {
        let ocr_repo = OcrRepository::new(self.db_manager.pool());
        ocr_repo.delete_text_block(block_id).await
    }

    /// 获取OCR进度
    pub async fn get_ocr_progress(&self, book_id: &str) -> Result<f64> {
        let ocr_repo = OcrRepository::new(self.db_manager.pool());
        ocr_repo.get_ocr_progress(book_id).await
    }
}
```

---

## ✅ 数据库架构设计完成

数据库架构设计已经完成，包括：

1. **三层架构设计** - 原始PDF层、OCR数据库层、重排显示层
2. **完整表结构** - 8个核心数据表，支持所有功能需求
3. **Rust实现** - 数据库管理器、模型定义、数据仓库
4. **位置坐标保持** - 确保文字编辑不影响原始位置
5. **版本控制** - 完整的编辑历史记录

下一步可以继续进行：
- **OCR引擎集成** - 集成Tesseract等OCR引擎
- **后台识别机制** - 实现持续识别和续传
- **重排显示系统** - 实现重排视图功能

---

*数据库架构版本: v1.0*  
*支持功能: OCR数据存储、位置保持、版本控制*
