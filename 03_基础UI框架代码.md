# 🎨 智能PDF阅读器基础UI框架代码

## 📱 主界面和导航系统

### lib/screens/main_screen.dart
```dart
/// 主界面 - 底部导航栏布局
/// 
/// 功能包括：
/// - 底部导航栏（书架、阅读、设置）
/// - 页面切换管理
/// - 全局状态管理

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:smart_pdf_reader/screens/library_screen.dart';
import 'package:smart_pdf_reader/screens/reading_screen.dart';
import 'package:smart_pdf_reader/screens/settings_screen.dart';

/// 当前选中的底部导航索引
final bottomNavIndexProvider = StateProvider<int>((ref) => 0);

class MainScreen extends ConsumerWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavIndexProvider);

    return Scaffold(
      body: IndexedStack(
        index: currentIndex,
        children: const [
          LibraryScreen(),    // 书架页面
          ReadingScreen(),    // 阅读页面
          SettingsScreen(),   // 设置页面
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: (index) {
          ref.read(bottomNavIndexProvider.notifier).state = index;
        },
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.library_books),
            label: '书架',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.menu_book),
            label: '阅读',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: '设置',
          ),
        ],
      ),
    );
  }
}
```

### lib/screens/library_screen.dart
```dart
/// 书架页面
/// 
/// 功能包括：
/// - 显示书籍列表
/// - 书籍分类和搜索
/// - 添加新书籍
/// - 书籍管理操作

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:smart_pdf_reader/widgets/book_card.dart';
import 'package:smart_pdf_reader/widgets/empty_state.dart';
import 'package:smart_pdf_reader/models/book_model.dart';

/// 书籍列表状态管理
final booksProvider = StateProvider<List<BookModel>>((ref) => []);

class LibraryScreen extends ConsumerWidget {
  const LibraryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final books = ref.watch(booksProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('我的书架'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
          IconButton(
            icon: const Icon(Icons.sort),
            onPressed: () => _showSortOptions(context),
          ),
        ],
      ),
      body: books.isEmpty
          ? const EmptyState(
              icon: Icons.library_books_outlined,
              title: '书架空空如也',
              subtitle: '点击右下角按钮添加您的第一本书',
            )
          : Padding(
              padding: const EdgeInsets.all(16),
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.7,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                ),
                itemCount: books.length,
                itemBuilder: (context, index) {
                  return BookCard(book: books[index]);
                },
              ),
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _addNewBook(context, ref),
        child: const Icon(Icons.add),
      ),
    );
  }

  /// 显示搜索对话框
  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('搜索书籍'),
        content: const TextField(
          decoration: InputDecoration(
            hintText: '输入书名或作者',
            prefixIcon: Icon(Icons.search),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              // TODO: 实现搜索功能
              Navigator.pop(context);
            },
            child: const Text('搜索'),
          ),
        ],
      ),
    );
  }

  /// 显示排序选项
  void _showSortOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.sort_by_alpha),
            title: const Text('按名称排序'),
            onTap: () {
              // TODO: 实现排序功能
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.access_time),
            title: const Text('按添加时间排序'),
            onTap: () {
              // TODO: 实现排序功能
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.star),
            title: const Text('按收藏排序'),
            onTap: () {
              // TODO: 实现排序功能
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  /// 添加新书籍
  void _addNewBook(BuildContext context, WidgetRef ref) {
    // TODO: 实现文件选择和书籍添加功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('文件选择功能开发中...')),
    );
  }
}
```

### lib/screens/reading_screen.dart
```dart
/// 阅读页面
/// 
/// 功能包括：
/// - PDF文档显示
/// - 阅读模式切换
/// - OCR重排功能
/// - 对照编辑模式

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:smart_pdf_reader/widgets/pdf_viewer.dart';
import 'package:smart_pdf_reader/widgets/reading_toolbar.dart';
import 'package:smart_pdf_reader/widgets/ocr_banner.dart';

/// 当前阅读的书籍
final currentBookProvider = StateProvider<String?>((ref) => null);

/// 阅读模式：原文、重排、对照
enum ReadingMode { original, reflow, comparison }

final readingModeProvider = StateProvider<ReadingMode>((ref) => ReadingMode.original);

class ReadingScreen extends ConsumerWidget {
  const ReadingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentBook = ref.watch(currentBookProvider);
    final readingMode = ref.watch(readingModeProvider);

    if (currentBook == null) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.menu_book_outlined,
                size: 64,
                color: Colors.grey,
              ),
              SizedBox(height: 16),
              Text(
                '请从书架选择一本书开始阅读',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      body: Column(
        children: [
          // OCR提示横幅
          const OcrBanner(),
          
          // 主要阅读区域
          Expanded(
            child: _buildReadingArea(readingMode),
          ),
          
          // 阅读工具栏
          const ReadingToolbar(),
        ],
      ),
    );
  }

  /// 构建阅读区域
  Widget _buildReadingArea(ReadingMode mode) {
    switch (mode) {
      case ReadingMode.original:
        return const PdfViewer();
      case ReadingMode.reflow:
        return const Center(
          child: Text('重排视图开发中...'),
        );
      case ReadingMode.comparison:
        return const Row(
          children: [
            Expanded(child: PdfViewer()),
            VerticalDivider(width: 1),
            Expanded(child: Center(child: Text('重排视图开发中...'))),
          ],
        );
    }
  }
}
```

---

## 🧩 基础组件库

### lib/widgets/book_card.dart
```dart
/// 书籍卡片组件
/// 
/// 显示书籍封面、标题、作者等信息

import 'package:flutter/material.dart';
import 'package:smart_pdf_reader/models/book_model.dart';

class BookCard extends StatelessWidget {
  final BookModel book;

  const BookCard({
    super.key,
    required this.book,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: () => _openBook(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 书籍封面
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  image: book.coverPath != null
                      ? DecorationImage(
                          image: AssetImage(book.coverPath!),
                          fit: BoxFit.cover,
                        )
                      : null,
                ),
                child: book.coverPath == null
                    ? const Icon(
                        Icons.picture_as_pdf,
                        size: 48,
                        color: Colors.grey,
                      )
                    : null,
              ),
            ),
            
            // 书籍信息
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      book.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      book.author ?? '未知作者',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const Spacer(),
                    Row(
                      children: [
                        Icon(
                          Icons.schedule,
                          size: 12,
                          color: Colors.grey[500],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _formatProgress(book.progress),
                          style: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 打开书籍
  void _openBook(BuildContext context) {
    // TODO: 实现打开书籍功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('打开《${book.title}》')),
    );
  }

  /// 格式化阅读进度
  String _formatProgress(double progress) {
    if (progress == 0) return '未开始';
    if (progress >= 1) return '已完成';
    return '${(progress * 100).toInt()}%';
  }
}
```

### lib/widgets/empty_state.dart
```dart
/// 空状态组件
/// 
/// 用于显示空列表、无数据等状态

import 'package:flutter/material.dart';

class EmptyState extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Widget? action;

  const EmptyState({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    this.action,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            if (action != null) ...[
              const SizedBox(height: 24),
              action!,
            ],
          ],
        ),
      ),
    );
  }
}
```

### lib/widgets/ocr_banner.dart
```dart
/// OCR提示横幅组件
/// 
/// 在扫描版PDF顶部显示OCR重排提示

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// OCR横幅显示状态
final ocrBannerVisibleProvider = StateProvider<bool>((ref) => true);

class OcrBanner extends ConsumerWidget {
  const OcrBanner({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isVisible = ref.watch(ocrBannerVisibleProvider);

    if (!isVisible) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        border: Border(
          bottom: BorderSide(color: Colors.blue[200]!),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.auto_fix_high,
            color: Colors.blue[700],
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              '这是扫描版文档，点击重新排版获得更好的阅读体验',
              style: TextStyle(
                color: Colors.blue[700],
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed: () => _startOcr(context, ref),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[700],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              minimumSize: Size.zero,
            ),
            child: const Text('重新排版'),
          ),
          const SizedBox(width: 8),
          TextButton(
            onPressed: () => _dismissBanner(ref),
            style: TextButton.styleFrom(
              foregroundColor: Colors.blue[700],
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              minimumSize: Size.zero,
            ),
            child: const Text('不再提示'),
          ),
          IconButton(
            onPressed: () => _dismissBanner(ref),
            icon: Icon(
              Icons.close,
              color: Colors.blue[700],
              size: 18,
            ),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(
              minWidth: 24,
              minHeight: 24,
            ),
          ),
        ],
      ),
    );
  }

  /// 开始OCR识别
  void _startOcr(BuildContext context, WidgetRef ref) {
    // TODO: 实现OCR识别功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('OCR识别功能开发中...')),
    );
    _dismissBanner(ref);
  }

  /// 隐藏横幅
  void _dismissBanner(WidgetRef ref) {
    ref.read(ocrBannerVisibleProvider.notifier).state = false;
  }
}
```

### lib/widgets/pdf_viewer.dart
```dart
/// PDF查看器组件
/// 
/// 基于syncfusion_flutter_pdfviewer实现

import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

class PdfViewer extends StatefulWidget {
  final String? filePath;

  const PdfViewer({
    super.key,
    this.filePath,
  });

  @override
  State<PdfViewer> createState() => _PdfViewerState();
}

class _PdfViewerState extends State<PdfViewer> {
  late PdfViewerController _pdfViewerController;

  @override
  void initState() {
    super.initState();
    _pdfViewerController = PdfViewerController();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.filePath == null) {
      return const Center(
        child: Text('请选择PDF文件'),
      );
    }

    return SfPdfViewer.file(
      widget.filePath!,
      controller: _pdfViewerController,
      onDocumentLoaded: (PdfDocumentLoadedDetails details) {
        // 文档加载完成
        debugPrint('PDF文档加载完成，共${details.document.pages.count}页');
      },
      onPageChanged: (PdfPageChangedDetails details) {
        // 页面变化
        debugPrint('当前页面：${details.newPageNumber}');
      },
    );
  }

  @override
  void dispose() {
    _pdfViewerController.dispose();
    super.dispose();
  }
}
```

### lib/widgets/reading_toolbar.dart
```dart
/// 阅读工具栏组件
/// 
/// 提供阅读相关的操作按钮

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:smart_pdf_reader/screens/reading_screen.dart';

class ReadingToolbar extends ConsumerWidget {
  const ReadingToolbar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final readingMode = ref.watch(readingModeProvider);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _ToolbarButton(
            icon: Icons.article,
            label: '原文',
            isSelected: readingMode == ReadingMode.original,
            onPressed: () {
              ref.read(readingModeProvider.notifier).state = ReadingMode.original;
            },
          ),
          _ToolbarButton(
            icon: Icons.view_stream,
            label: '重排',
            isSelected: readingMode == ReadingMode.reflow,
            onPressed: () {
              ref.read(readingModeProvider.notifier).state = ReadingMode.reflow;
            },
          ),
          _ToolbarButton(
            icon: Icons.compare,
            label: '对照',
            isSelected: readingMode == ReadingMode.comparison,
            onPressed: () {
              ref.read(readingModeProvider.notifier).state = ReadingMode.comparison;
            },
          ),
          _ToolbarButton(
            icon: Icons.volume_up,
            label: '语音',
            onPressed: () {
              // TODO: 实现语音阅读功能
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('语音阅读功能开发中...')),
              );
            },
          ),
          _ToolbarButton(
            icon: Icons.more_vert,
            label: '更多',
            onPressed: () => _showMoreOptions(context),
          ),
        ],
      ),
    );
  }

  /// 显示更多选项
  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.bookmark),
            title: const Text('添加书签'),
            onTap: () {
              Navigator.pop(context);
              // TODO: 实现添加书签功能
            },
          ),
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('分享'),
            onTap: () {
              Navigator.pop(context);
              // TODO: 实现分享功能
            },
          ),
          ListTile(
            leading: const Icon(Icons.file_download),
            title: const Text('导出'),
            onTap: () {
              Navigator.pop(context);
              // TODO: 实现导出功能
            },
          ),
        ],
      ),
    );
  }
}

/// 工具栏按钮组件
class _ToolbarButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final bool isSelected;
  final VoidCallback onPressed;

  const _ToolbarButton({
    required this.icon,
    required this.label,
    this.isSelected = false,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final color = isSelected 
        ? Theme.of(context).primaryColor 
        : Colors.grey[600];

    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
```

---

## 📊 数据模型

### lib/models/book_model.dart
```dart
/// 书籍数据模型
/// 
/// 定义书籍的基本信息和状态

class BookModel {
  final String id;
  final String title;
  final String? author;
  final String filePath;
  final String? coverPath;
  final int pageCount;
  final double progress;
  final DateTime addedAt;
  final DateTime? lastReadAt;
  final bool isFavorite;
  final bool hasOcrData;

  const BookModel({
    required this.id,
    required this.title,
    this.author,
    required this.filePath,
    this.coverPath,
    required this.pageCount,
    this.progress = 0.0,
    required this.addedAt,
    this.lastReadAt,
    this.isFavorite = false,
    this.hasOcrData = false,
  });

  /// 从JSON创建书籍对象
  factory BookModel.fromJson(Map<String, dynamic> json) {
    return BookModel(
      id: json['id'],
      title: json['title'],
      author: json['author'],
      filePath: json['filePath'],
      coverPath: json['coverPath'],
      pageCount: json['pageCount'],
      progress: json['progress']?.toDouble() ?? 0.0,
      addedAt: DateTime.parse(json['addedAt']),
      lastReadAt: json['lastReadAt'] != null 
          ? DateTime.parse(json['lastReadAt']) 
          : null,
      isFavorite: json['isFavorite'] ?? false,
      hasOcrData: json['hasOcrData'] ?? false,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'author': author,
      'filePath': filePath,
      'coverPath': coverPath,
      'pageCount': pageCount,
      'progress': progress,
      'addedAt': addedAt.toIso8601String(),
      'lastReadAt': lastReadAt?.toIso8601String(),
      'isFavorite': isFavorite,
      'hasOcrData': hasOcrData,
    };
  }

  /// 复制并修改属性
  BookModel copyWith({
    String? id,
    String? title,
    String? author,
    String? filePath,
    String? coverPath,
    int? pageCount,
    double? progress,
    DateTime? addedAt,
    DateTime? lastReadAt,
    bool? isFavorite,
    bool? hasOcrData,
  }) {
    return BookModel(
      id: id ?? this.id,
      title: title ?? this.title,
      author: author ?? this.author,
      filePath: filePath ?? this.filePath,
      coverPath: coverPath ?? this.coverPath,
      pageCount: pageCount ?? this.pageCount,
      progress: progress ?? this.progress,
      addedAt: addedAt ?? this.addedAt,
      lastReadAt: lastReadAt ?? this.lastReadAt,
      isFavorite: isFavorite ?? this.isFavorite,
      hasOcrData: hasOcrData ?? this.hasOcrData,
    );
  }
}
```

---

## ✅ 基础UI框架完成

基础UI框架已经搭建完成，包括：

1. **主界面和导航系统** - 底部导航栏布局
2. **书架页面** - 书籍列表和管理功能
3. **阅读页面** - PDF查看和模式切换
4. **基础组件库** - 可复用的UI组件
5. **数据模型** - 书籍信息数据结构

下一步可以继续进行：
- **数据库架构设计** - 实现三层数据库架构
- **OCR引擎集成** - 集成Tesseract等OCR引擎
- **功能逻辑实现** - 完善各个功能模块

---

*UI框架代码版本: v1.0*  
*适用于: Flutter 3.16+ & Material Design 3*
